import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Alert,
  Modal,
  ScrollView,
  Image,
  Share,
} from 'react-native';
import { Video } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  FadeIn,
  SlideInDown,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setPlaying,
  setCurrentTime,
  setDuration,
  setFullscreen,
  setBuffering,
  resetPlayer,
} from '../../store/slices/playerSlice';
import { fetchVideoById, incrementViewCount, likeVideo, addToWatchLater } from '../../store/slices/videoSlice';
import { Video as VideoType } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';
import { formatTime, formatViewCount } from '../../utils/formatters';

const { width, height } = Dimensions.get('window');

interface Props {
  videoId: string;
  onClose: () => void;
}

export default function VideoPlayerScreen({ videoId, onClose }: Props) {
  const dispatch = useAppDispatch();
  const { currentVideo } = useAppSelector((state) => state.video);
  const { isPlaying, currentTime, duration, isFullscreen } = useAppSelector((state) => state.player);
  const { user } = useAppSelector((state) => state.auth);

  const videoRef = useRef<Video>(null);
  const [showControls, setShowControls] = useState(true);
  const [hasViewBeenCounted, setHasViewBeenCounted] = useState(false);
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);
  const [subtitles, setSubtitles] = useState(false);
  const [quality, setQuality] = useState('auto');
  const [volume, setVolume] = useState(1.0);
  const [showSpeedSelector, setShowSpeedSelector] = useState(false);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isInWatchLater, setIsInWatchLater] = useState(false);

  // Animated values
  const controlsOpacity = useSharedValue(1);

  useEffect(() => {
    dispatch(fetchVideoById(videoId));
    dispatch(resetPlayer());
    return () => dispatch(resetPlayer());
  }, [dispatch, videoId]);

  useEffect(() => {
    if (showControls && isPlaying) {
      const timer = setTimeout(() => {
        controlsOpacity.value = withTiming(0, { duration: 300 });
        setShowControls(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showControls, isPlaying]);

  useEffect(() => {
    if (currentVideo && currentTime > 30 && !hasViewBeenCounted) {
      dispatch(incrementViewCount(currentVideo.id));
      setHasViewBeenCounted(true);
    }
  }, [currentTime, currentVideo, hasViewBeenCounted, dispatch]);

  const toggleControls = () => {
    hapticFeedback.light();
    if (showControls) {
      controlsOpacity.value = withTiming(0, { duration: 300 });
      setShowControls(false);
    } else {
      controlsOpacity.value = withTiming(1, { duration: 300 });
      setShowControls(true);
    }
  };

  const handleLike = async () => {
    hapticFeedback.medium();
    if (currentVideo && user) {
      await dispatch(likeVideo({ videoId: currentVideo.id, userId: user.id }));
      setIsLiked(!isLiked);
    }
  };

  const handleWatchLater = async () => {
    hapticFeedback.light();
    if (currentVideo && user) {
      await dispatch(addToWatchLater({ videoId: currentVideo.id, userId: user.id }));
      setIsInWatchLater(!isInWatchLater);
      Alert.alert('Added to Watch Later', 'Video saved for later viewing');
    }
  };

  const handleShare = async () => {
    hapticFeedback.light();
    if (currentVideo) {
      try {
        await Share.share({
          message: `Check out this video: ${currentVideo.title}`,
          url: currentVideo.video_url,
        });
      } catch (error) {
        console.error('Error sharing video:', error);
      }
    }
  };

  const toggleComments = () => {
    hapticFeedback.medium();
    setShowComments(!showComments);
  };

  const toggleSubtitles = () => {
    hapticFeedback.light();
    setSubtitles(!subtitles);
  };

  const handleQualityChange = (newQuality: string) => {
    hapticFeedback.light();
    setQuality(newQuality);
    setShowQualitySelector(false);
  };

  const getProgressPercentage = () => {
    if (duration === 0) return 0;
    return (currentTime / duration) * 100;
  };

  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      dispatch(setCurrentTime(status.positionMillis / 1000));
      dispatch(setDuration(status.durationMillis / 1000));
      dispatch(setPlaying(status.isPlaying));
      dispatch(setBuffering(status.isBuffering));
    }
  };

  const handlePlayPause = async () => {
    hapticFeedback.light();
    if (videoRef.current) {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
      dispatch(setPlaying(!isPlaying));
    }
  };

  const handleSeek = async (position: number) => {
    if (videoRef.current && duration > 0) {
      const seekTime = (position * duration) / 100;
      await videoRef.current.setPositionAsync(seekTime * 1000);
      dispatch(setCurrentTime(seekTime));
    }
  };

  const handleRewind = async () => {
    hapticFeedback.light();
    if (videoRef.current && currentTime > 10) {
      const newTime = Math.max(0, currentTime - 10);
      await videoRef.current.setPositionAsync(newTime * 1000);
      dispatch(setCurrentTime(newTime));
    }
  };

  const handleFastForward = async () => {
    hapticFeedback.light();
    if (videoRef.current && currentTime < duration - 10) {
      const newTime = Math.min(duration, currentTime + 10);
      await videoRef.current.setPositionAsync(newTime * 1000);
      dispatch(setCurrentTime(newTime));
    }
  };

  const handleFullscreen = async () => {
    hapticFeedback.medium();
    if (videoRef.current) {
      if (isFullscreen) {
        await videoRef.current.dismissFullscreenPlayer();
      } else {
        await videoRef.current.presentFullscreenPlayer();
      }
      dispatch(setFullscreen(!isFullscreen));
    }
  };

  const handlePlaybackSpeedChange = async (speed: number) => {
    hapticFeedback.light();
    setPlaybackSpeed(speed);
    if (videoRef.current) {
      await videoRef.current.setRateAsync(speed, true);
    }
    setShowSpeedSelector(false);
  };

  const handleVolumeChange = async (newVolume: number) => {
    setVolume(newVolume);
    if (videoRef.current) {
      await videoRef.current.setVolumeAsync(newVolume);
    }
  };

  const checkAccess = () => {
    if (!currentVideo) return false;
    if (!currentVideo.is_premium) return true;
    return user?.subscription_status === 'active';
  };

  // Loading state
  if (!currentVideo) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading video...</Text>
      </View>
    );
  }

  // Access denied state
  if (!checkAccess()) {
    return (
      <View style={styles.accessDeniedContainer}>
        <Text style={styles.accessDeniedTitle}>Premium Content</Text>
        <Text style={styles.accessDeniedText}>
          This video requires a premium subscription to watch.
        </Text>
        <TouchableOpacity style={styles.upgradeButton}>
          <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Animated styles
  const controlsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: controlsOpacity.value,
  }));

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      {/* Video Player */}
      <View style={styles.videoContainer}>
        <TouchableOpacity
          style={styles.videoTouchable}
          activeOpacity={1}
          onPress={toggleControls}
        >
          <Video
            ref={videoRef}
            source={{ uri: currentVideo.video_url }}
            style={styles.video}
            useNativeControls={false}
            resizeMode="contain"
            onPlaybackStatusUpdate={onPlaybackStatusUpdate}
            shouldPlay={false}
          />

          {/* Enhanced Controls Overlay */}
          <Animated.View style={[styles.controlsOverlay, controlsAnimatedStyle]}>
            <LinearGradient
              colors={['rgba(0,0,0,0.8)', 'transparent', 'rgba(0,0,0,0.8)']}
              style={styles.controlsGradient}
            >
              {/* Top Controls Bar */}
              <Animated.View style={styles.topControls} entering={FadeIn.delay(100)}>
                <TouchableOpacity style={styles.modernBackButton} onPress={onClose}>
                  <Ionicons name="chevron-back" size={28} color="#FFFFFF" />
                </TouchableOpacity>
                
                <View style={styles.topCenterInfo}>
                  <Text style={styles.modernVideoTitle} numberOfLines={1}>
                    {currentVideo.title}
                  </Text>
                  <Text style={styles.modernVideoCreator}>
                    {currentVideo.creator.username} • {formatViewCount(currentVideo.view_count)} views
                  </Text>
                </View>

                <View style={styles.topRightControls}>
                  <TouchableOpacity style={styles.modernControlButton} onPress={() => setShowAdvancedControls(true)}>
                    <Ionicons name="settings-outline" size={24} color="#FFFFFF" />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.modernControlButton} onPress={handleShare}>
                    <Ionicons name="share-outline" size={24} color="#FFFFFF" />
                  </TouchableOpacity>
                </View>
              </Animated.View>

              {/* Center Play Controls */}
              <Animated.View style={styles.centerControls} entering={FadeIn.delay(200)}>
                <TouchableOpacity style={styles.modernRewindButton} onPress={handleRewind}>
                  <Ionicons name="play-back" size={32} color="#FFFFFF" />
                  <Text style={styles.skipText}>10s</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.modernPlayButton} onPress={handlePlayPause}>
                  <LinearGradient
                    colors={[GoGoColors.primary, GoGoColors.highlightGold]}
                    style={styles.playButtonGradient}
                  >
                    <Ionicons
                      name={isPlaying ? 'pause' : 'play'}
                      size={40}
                      color="#FFFFFF"
                      style={isPlaying ? {} : { marginLeft: 4 }}
                    />
                  </LinearGradient>
                </TouchableOpacity>

                <TouchableOpacity style={styles.modernForwardButton} onPress={handleFastForward}>
                  <Ionicons name="play-forward" size={32} color="#FFFFFF" />
                  <Text style={styles.skipText}>10s</Text>
                </TouchableOpacity>
              </Animated.View>

              {/* Bottom Controls */}
              <Animated.View style={styles.bottomControls} entering={FadeIn.delay(300)}>
                {/* Progress Bar */}
                <View style={styles.modernProgressContainer}>
                  <Text style={styles.modernTimeText}>{formatTime(currentTime)}</Text>
                  <View style={styles.modernProgressBar}>
                    <Slider
                      style={styles.progressSlider}
                      minimumValue={0}
                      maximumValue={100}
                      value={getProgressPercentage()}
                      onValueChange={(value) => handleSeek(value)}
                      minimumTrackTintColor={GoGoColors.primary}
                      maximumTrackTintColor="rgba(255,255,255,0.3)"
                      thumbStyle={styles.progressThumbStyle}
                    />
                  </View>
                  <Text style={styles.modernTimeText}>{formatTime(duration)}</Text>
                </View>

                {/* Control Buttons Row */}
                <View style={styles.modernControlsRow}>
                  <TouchableOpacity style={styles.modernControlButton} onPress={() => setShowSpeedSelector(true)}>
                    <Text style={styles.speedButtonText}>{playbackSpeed}x</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.modernControlButton} onPress={toggleSubtitles}>
                    <Ionicons
                      name="chatbox-outline"
                      size={20}
                      color={subtitles ? GoGoColors.primary : "#FFFFFF"}
                    />
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.modernControlButton} onPress={() => setShowQualitySelector(true)}>
                    <Text style={styles.qualityButtonText}>{quality.toUpperCase()}</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.modernControlButton} onPress={handleFullscreen}>
                    <Ionicons
                      name={isFullscreen ? "contract-outline" : "expand-outline"}
                      size={20}
                      color="#FFFFFF"
                    />
                  </TouchableOpacity>
                </View>
              </Animated.View>
            </LinearGradient>
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Enhanced Video Information Panel */}
      <Animated.View style={styles.modernVideoInfo} entering={SlideInDown.delay(400)}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Video Title and Creator */}
          <View style={styles.videoHeaderSection}>
            <Text style={styles.fullVideoTitle}>{currentVideo.title}</Text>
            <Text style={styles.videoStats}>
              {formatViewCount(currentVideo.view_count)} views • {new Date(currentVideo.created_at).toLocaleDateString()}
            </Text>
          </View>

          {/* Action Buttons Row */}
          <View style={styles.actionButtonsRow}>
            <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
              <LinearGradient
                colors={isLiked ? [GoGoColors.error, '#FF6B6B'] : ['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons
                  name={isLiked ? "heart" : "heart-outline"}
                  size={24}
                  color={isLiked ? "#FFFFFF" : GoGoColors.textPrimary}
                />
                <Text style={[styles.actionButtonText, { color: isLiked ? "#FFFFFF" : GoGoColors.textPrimary }]}>
                  {isLiked ? 'Liked' : 'Like'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleWatchLater}>
              <LinearGradient
                colors={isInWatchLater ? [GoGoColors.primary, GoGoColors.highlightGold] : ['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons
                  name={isInWatchLater ? "bookmark" : "bookmark-outline"}
                  size={24}
                  color={isInWatchLater ? "#FFFFFF" : GoGoColors.textPrimary}
                />
                <Text style={[styles.actionButtonText, { color: isInWatchLater ? "#FFFFFF" : GoGoColors.textPrimary }]}>
                  {isInWatchLater ? 'Saved' : 'Save'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
              <LinearGradient
                colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons name="share-outline" size={24} color={GoGoColors.textPrimary} />
                <Text style={styles.actionButtonText}>Share</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={() => Alert.alert('Download', 'Download feature coming soon!')}>
              <LinearGradient
                colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons name="download-outline" size={24} color={GoGoColors.textPrimary} />
                <Text style={styles.actionButtonText}>Download</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* Creator Information */}
          <View style={styles.creatorSection}>
            <View style={styles.creatorInfo}>
              <Image
                source={{ uri: currentVideo.creator.avatar_url || 'https://via.placeholder.com/50' }}
                style={styles.creatorAvatar}
              />
              <View style={styles.creatorDetails}>
                <Text style={styles.creatorName}>{currentVideo.creator.username}</Text>
                <Text style={styles.creatorSubscribers}>1.2M subscribers</Text>
              </View>
            </View>
            <TouchableOpacity style={styles.subscribeButton}>
              <LinearGradient
                colors={[GoGoColors.primary, GoGoColors.highlightGold]}
                style={styles.subscribeGradient}
              >
                <Text style={styles.subscribeText}>Subscribe</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* Video Description */}
          <View style={styles.descriptionSection}>
            <Text style={styles.descriptionTitle}>Description</Text>
            <Text style={styles.descriptionText} numberOfLines={3}>
              {currentVideo.description || 'No description available for this video.'}
            </Text>
            <TouchableOpacity>
              <Text style={styles.showMoreText}>Show more</Text>
            </TouchableOpacity>
          </View>

          {/* Comments and Related Videos Buttons */}
          <View style={styles.bottomActionsRow}>
            <TouchableOpacity style={styles.bottomActionButton} onPress={toggleComments}>
              <Ionicons name="chatbubble-outline" size={20} color={GoGoColors.textPrimary} />
              <Text style={styles.bottomActionText}>Comments (24)</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.bottomActionButton} onPress={() => Alert.alert('Related Videos', 'Related videos coming soon!')}>
              <Ionicons name="list-outline" size={20} color={GoGoColors.textPrimary} />
              <Text style={styles.bottomActionText}>Related Videos</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </Animated.View>

      {/* Speed Selector Modal */}
      {showSpeedSelector && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.speedModal} entering={SlideInDown}>
              <Text style={styles.modalTitle}>Playback Speed</Text>
              {[0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0].map((speed) => (
                <TouchableOpacity
                  key={speed}
                  style={[styles.speedOption, playbackSpeed === speed && styles.speedOptionActive]}
                  onPress={() => handlePlaybackSpeedChange(speed)}
                >
                  <Text style={[styles.speedOptionText, playbackSpeed === speed && styles.speedOptionTextActive]}>
                    {speed}x {speed === 1.0 ? '(Normal)' : ''}
                  </Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          </View>
        </Modal>
      )}

      {/* Quality Selector Modal */}
      {showQualitySelector && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.qualityModal} entering={SlideInDown}>
              <Text style={styles.modalTitle}>Video Quality</Text>
              {['auto', '1080p', '720p', '480p', '360p'].map((qual) => (
                <TouchableOpacity
                  key={qual}
                  style={[styles.qualityOption, quality === qual && styles.qualityOptionActive]}
                  onPress={() => handleQualityChange(qual)}
                >
                  <Text style={[styles.qualityOptionText, quality === qual && styles.qualityOptionTextActive]}>
                    {qual.toUpperCase()} {qual === 'auto' ? '(Recommended)' : ''}
                  </Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          </View>
        </Modal>
      )}

      {/* Advanced Controls Modal */}
      {showAdvancedControls && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.advancedModal} entering={SlideInDown}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Advanced Controls</Text>
                <TouchableOpacity onPress={() => setShowAdvancedControls(false)}>
                  <Ionicons name="close" size={24} color={GoGoColors.textPrimary} />
                </TouchableOpacity>
              </View>

              <View style={styles.advancedControlsContent}>
                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Subtitles</Text>
                  <TouchableOpacity
                    style={[styles.toggleButton, subtitles && styles.toggleButtonActive]}
                    onPress={toggleSubtitles}
                  >
                    <Text style={[styles.toggleButtonText, subtitles && styles.toggleButtonTextActive]}>
                      {subtitles ? 'ON' : 'OFF'}
                    </Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Auto-play Next</Text>
                  <TouchableOpacity style={styles.toggleButton}>
                    <Text style={styles.toggleButtonText}>OFF</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Loop Video</Text>
                  <TouchableOpacity style={styles.toggleButton}>
                    <Text style={styles.toggleButtonText}>OFF</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingText: {
    color: GoGoColors.textPrimary,
    fontSize: 16,
    marginTop: 10,
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
    padding: 20,
  },
  accessDeniedTitle: {
    color: GoGoColors.textPrimary,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  accessDeniedText: {
    color: GoGoColors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  upgradeButton: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 15,
  },
  upgradeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  // Modern Video Player Styles
  videoContainer: {
    height: height * 0.35,
    backgroundColor: '#000000',
    position: 'relative',
  },
  videoTouchable: {
    width: '100%',
    height: '100%',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  controlsGradient: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  // Top Controls
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modernBackButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  topCenterInfo: {
    flex: 1,
    marginHorizontal: 16,
  },
  modernVideoTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modernVideoCreator: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
    marginTop: 2,
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  topRightControls: {
    flexDirection: 'row',
    gap: 8,
  },
  modernControlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Center Controls
  centerControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 40,
  },
  modernRewindButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  modernForwardButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  skipText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 10,
    marginTop: 4,
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modernPlayButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  playButtonGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Bottom Controls
  bottomControls: {
    gap: 16,
  },
  modernProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  modernTimeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    minWidth: 40,
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modernProgressBar: {
    flex: 1,
    height: 40,
  },
  progressSlider: {
    width: '100%',
    height: 40,
  },
  progressThumbStyle: {
    width: 16,
    height: 16,
    backgroundColor: GoGoColors.primary,
  },
  modernControlsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  speedButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  qualityButtonText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  // Modern Video Info Panel
  modernVideoInfo: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingTop: 24,
  },
  videoHeaderSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  fullVideoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    lineHeight: 28,
    marginBottom: 8,
  },
  videoStats: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  // Action Buttons
  actionButtonsRow: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  actionButtonGradient: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
    gap: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
  },
  // Creator Section
  creatorSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  creatorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  creatorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  creatorDetails: {
    flex: 1,
  },
  creatorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  creatorSubscribers: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  subscribeButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  subscribeGradient: {
    paddingHorizontal: 24,
    paddingVertical: 10,
  },
  subscribeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  // Description Section
  descriptionSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  descriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  showMoreText: {
    fontSize: 14,
    color: GoGoColors.primary,
    fontWeight: '600',
  },
  // Bottom Actions
  bottomActionsRow: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  bottomActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  bottomActionText: {
    fontSize: 14,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  // Modal Overlays
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  speedModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 300,
  },
  speedOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: 'transparent',
  },
  speedOptionActive: {
    backgroundColor: GoGoColors.primary,
  },
  speedOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    fontWeight: '500',
  },
  speedOptionTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  qualityModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 300,
  },
  qualityOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: 'transparent',
  },
  qualityOptionActive: {
    backgroundColor: GoGoColors.primary,
  },
  qualityOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    fontWeight: '500',
  },
  qualityOptionTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  // Advanced Controls Modal
  advancedModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 350,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  advancedControlsContent: {
    paddingTop: 16,
  },
  controlRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  controlLabel: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  toggleButton: {
    backgroundColor: GoGoColors.backgroundLight,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    minWidth: 60,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: GoGoColors.primary,
  },
  toggleButtonText: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    fontWeight: 'bold',
  },
  toggleButtonTextActive: {
    color: '#FFFFFF',
  },
});

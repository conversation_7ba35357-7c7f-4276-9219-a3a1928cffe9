import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  StatusBar,
  Alert,
  Modal,
  ScrollView,
  FlatList,
  Image,
  Share,

} from 'react-native';
import { Video } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import Slider from '@react-native-community/slider';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  runOnJS,
  useAnimatedGestureHandler,
  interpolate,
  Extrapolate,
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
} from 'react-native-reanimated';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setPlaying,
  setCurrentTime,
  setDuration,
  setFullscreen,
  setBuffering,
  resetPlayer,
} from '../../store/slices/playerSlice';
import { fetchVideoById, incrementViewCount, likeVideo, addToWatchLater } from '../../store/slices/videoSlice';
import { Video as VideoType } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { useResponsiveLayout } from '../../utils/responsive';
import { hapticFeedback } from '../../utils/animations';
import { formatTime, formatViewCount } from '../../utils/formatters';

const { width, height } = Dimensions.get('window');

interface Props {
  videoId: string;
  onClose: () => void;
}

export default function VideoPlayerScreen({ videoId, onClose }: Props) {
  const dispatch = useAppDispatch();
  const { currentVideo } = useAppSelector((state) => state.video);
  const { isPlaying, currentTime, duration, isFullscreen } = useAppSelector((state) => state.player);
  const { user } = useAppSelector((state) => state.auth);
  const layout = useResponsiveLayout();

  const videoRef = useRef<Video>(null);
  const [showControls, setShowControls] = useState(true);
  const [hasViewBeenCounted, setHasViewBeenCounted] = useState(false);
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0);
  const [subtitles, setSubtitles] = useState(false);
  const [quality, setQuality] = useState('auto');
  const [volume, setVolume] = useState(1.0);
  const [brightness, setBrightness] = useState(1.0);
  const [isBuffering, setIsBuffering] = useState(false);
  const [showSpeedSelector, setShowSpeedSelector] = useState(false);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [showRelatedVideos, setShowRelatedVideos] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isInWatchLater, setIsInWatchLater] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showVideoInfo, setShowVideoInfo] = useState(true);
  const [gestureEnabled, setGestureEnabled] = useState(true);

  // Animated values
  const controlsOpacity = useSharedValue(1);
  const videoInfoTranslateY = useSharedValue(0);
  const commentsTranslateY = useSharedValue(height);
  const relatedVideosTranslateY = useSharedValue(height);
  const brightnessOverlay = useSharedValue(0);
  const volumeIndicator = useSharedValue(0);
  const seekIndicator = useSharedValue(0);

  useEffect(() => {
    // Load video data
    dispatch(fetchVideoById(videoId));
    
    // Reset player state
    dispatch(resetPlayer());

    return () => {
      dispatch(resetPlayer());
    };
  }, [dispatch, videoId]);

  useEffect(() => {
    // Auto-hide controls after 4 seconds with smooth animation
    if (showControls && isPlaying) {
      const timer = setTimeout(() => {
        controlsOpacity.value = withTiming(0, { duration: 300 });
        setShowControls(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [showControls, isPlaying]);

  // Gesture handlers for enhanced video interaction
  const panGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startY = _.absoluteY;
      context.startX = _.absoluteX;
    },
    onActive: (event, context) => {
      if (!gestureEnabled) return;

      const deltaY = event.absoluteY - context.startY;
      const deltaX = event.absoluteX - context.startX;

      // Vertical gestures for brightness (left side) and volume (right side)
      if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 20) {
        if (event.absoluteX < width / 2) {
          // Left side - brightness control
          const brightnessChange = -deltaY / (height / 2);
          const newBrightness = Math.max(0.1, Math.min(1, brightness + brightnessChange));
          brightnessOverlay.value = 1 - newBrightness;
          runOnJS(setBrightness)(newBrightness);
        } else {
          // Right side - volume control
          const volumeChange = -deltaY / (height / 2);
          const newVolume = Math.max(0, Math.min(1, volume + volumeChange));
          volumeIndicator.value = withSpring(1);
          runOnJS(setVolume)(newVolume);
        }
      }

      // Horizontal gestures for seeking
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30) {
        const seekChange = deltaX / width;
        seekIndicator.value = withSpring(1);
        // Implement seeking logic here
      }
    },
    onEnd: () => {
      volumeIndicator.value = withTiming(0, { duration: 1000 });
      seekIndicator.value = withTiming(0, { duration: 1000 });
    },
  });

  // Enhanced functions
  const toggleControls = () => {
    hapticFeedback.light();
    if (showControls) {
      controlsOpacity.value = withTiming(0, { duration: 300 });
      setShowControls(false);
    } else {
      controlsOpacity.value = withTiming(1, { duration: 300 });
      setShowControls(true);
    }
  };

  const handleLike = async () => {
    hapticFeedback.medium();
    if (currentVideo) {
      await dispatch(likeVideo(currentVideo.id));
      setIsLiked(!isLiked);
    }
  };

  const handleWatchLater = async () => {
    hapticFeedback.light();
    if (currentVideo) {
      await dispatch(addToWatchLater(currentVideo.id));
      setIsInWatchLater(!isInWatchLater);
      Alert.alert('Added to Watch Later', 'Video saved for later viewing');
    }
  };

  const handleShare = async () => {
    hapticFeedback.light();
    if (currentVideo) {
      try {
        await Share.share({
          message: `Check out this video: ${currentVideo.title}`,
          url: currentVideo.video_url,
        });
      } catch (error) {
        console.error('Error sharing video:', error);
      }
    }
  };

  const toggleComments = () => {
    hapticFeedback.medium();
    if (showComments) {
      commentsTranslateY.value = withSpring(height);
      setShowComments(false);
    } else {
      commentsTranslateY.value = withSpring(0);
      setShowComments(true);
    }
  };

  const toggleRelatedVideos = () => {
    hapticFeedback.medium();
    if (showRelatedVideos) {
      relatedVideosTranslateY.value = withSpring(height);
      setShowRelatedVideos(false);
    } else {
      relatedVideosTranslateY.value = withSpring(0);
      setShowRelatedVideos(true);
    }
  };

  const toggleSubtitles = () => {
    hapticFeedback.light();
    setSubtitles(!subtitles);
  };

  const handleQualityChange = (newQuality: string) => {
    hapticFeedback.light();
    setQuality(newQuality);
    setShowQualitySelector(false);
  };

  const getProgressPercentage = () => {
    if (duration === 0) return 0;
    return (currentTime / duration) * 100;
  };

  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      dispatch(setCurrentTime(status.positionMillis / 1000));
      dispatch(setDuration(status.durationMillis / 1000));
      dispatch(setPlaying(status.isPlaying));
      dispatch(setBuffering(status.isBuffering));
      setIsBuffering(status.isBuffering);
    }
  };

  const checkAccess = () => {
    // Mock access check - implement your own logic
    return true;
  };

  useEffect(() => {
    // Count view when video reaches 30 seconds or 25% of duration
    if (currentVideo && currentTime > 30 && !hasViewBeenCounted) {
      dispatch(incrementViewCount(currentVideo.id));
      setHasViewBeenCounted(true);
    }
  }, [currentTime, currentVideo, hasViewBeenCounted, dispatch]);

  const handlePlayPause = async () => {
    hapticFeedback.light();
    if (videoRef.current) {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
      dispatch(setPlaying(!isPlaying));
    }
  };

  const handleSeek = async (position: number) => {
    if (videoRef.current && duration > 0) {
      const seekTime = (position * duration) / 100;
      await videoRef.current.setPositionAsync(seekTime * 1000);
      dispatch(setCurrentTime(seekTime));
    }
  };

  const handleRewind = async () => {
    hapticFeedback.light();
    if (videoRef.current && currentTime > 10) {
      const newTime = Math.max(0, currentTime - 10);
      await videoRef.current.setPositionAsync(newTime * 1000);
      dispatch(setCurrentTime(newTime));
    }
  };

  const handleFastForward = async () => {
    hapticFeedback.light();
    if (videoRef.current && currentTime < duration - 10) {
      const newTime = Math.min(duration, currentTime + 10);
      await videoRef.current.setPositionAsync(newTime * 1000);
      dispatch(setCurrentTime(newTime));
    }
  };

  const handleFullscreen = async () => {
    hapticFeedback.medium();
    if (videoRef.current) {
      if (isFullscreen) {
        await videoRef.current.dismissFullscreenPlayer();
      } else {
        await videoRef.current.presentFullscreenPlayer();
      }
      dispatch(setFullscreen(!isFullscreen));
    }
  };

  const handlePlaybackSpeedChange = async (speed: number) => {
    hapticFeedback.light();
    setPlaybackSpeed(speed);
    if (videoRef.current) {
      await videoRef.current.setRateAsync(speed, true);
    }
    setShowSpeedSelector(false);
  };



  const toggleSubtitles = () => {
    hapticFeedback.light();
    setSubtitles(!subtitles);
  };

  const handleVolumeChange = async (newVolume: number) => {
    setVolume(newVolume);
    if (videoRef.current) {
      await videoRef.current.setVolumeAsync(newVolume);
    }
  };

  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      dispatch(setCurrentTime(status.positionMillis / 1000));
      dispatch(setDuration(status.durationMillis / 1000));
      dispatch(setPlaying(status.isPlaying));
      dispatch(setBuffering(status.isBuffering));
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = () => {
    return duration > 0 ? (currentTime / duration) * 100 : 0;
  };

  const checkAccess = () => {
    if (!currentVideo) return false;
    if (!currentVideo.is_premium) return true;
    
    // Check if user has purchased this video or has subscription
    // This would be implemented with actual purchase/subscription logic
    return user?.subscription_status === 'active';
  };

  if (!currentVideo) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading video...</Text>
      </View>
    );
  }

  if (!checkAccess()) {
    return (
      <View style={styles.accessDeniedContainer}>
        <Text style={styles.accessDeniedTitle}>Premium Content</Text>
        <Text style={styles.accessDeniedText}>
          This video requires a premium subscription or purchase to watch.
        </Text>
        <TouchableOpacity style={styles.upgradeButton}>
          <Text style={styles.upgradeButtonText}>Upgrade Now</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Animated styles
  const controlsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: controlsOpacity.value,
  }));

  const videoInfoAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: videoInfoTranslateY.value }],
  }));

  const commentsAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: commentsTranslateY.value }],
  }));

  const relatedVideosAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: relatedVideosTranslateY.value }],
  }));

  const brightnessOverlayStyle = useAnimatedStyle(() => ({
    opacity: brightnessOverlay.value,
  }));

  return (
    <View style={styles.container}>
      <StatusBar hidden />

      {/* Video Player with Gesture Handler */}
      <PanGestureHandler onGestureEvent={panGestureHandler}>
        <Animated.View style={styles.videoContainer}>
          <TouchableOpacity
            style={styles.videoTouchable}
            activeOpacity={1}
            onPress={toggleControls}
          >
            <Video
              ref={videoRef}
              source={{ uri: currentVideo.video_url }}
              style={styles.video}
              useNativeControls={false}
              resizeMode="contain"
              onPlaybackStatusUpdate={onPlaybackStatusUpdate}
              shouldPlay={false}
            />

            {/* Brightness Overlay */}
            <Animated.View style={[styles.brightnessOverlay, brightnessOverlayStyle]} />

            {/* Enhanced Controls Overlay */}
            <Animated.View style={[styles.controlsOverlay, controlsAnimatedStyle]}>
              <LinearGradient
                colors={['rgba(0,0,0,0.8)', 'transparent', 'rgba(0,0,0,0.8)']}
                style={styles.controlsGradient}
              >
                {/* Top Controls Bar */}
                <Animated.View style={styles.topControls} entering={FadeIn.delay(100)}>
                  <TouchableOpacity style={styles.modernBackButton} onPress={onClose}>
                    <Ionicons name="chevron-back" size={28} color="#FFFFFF" />
                  </TouchableOpacity>

                  <View style={styles.topCenterInfo}>
                    <Text style={styles.modernVideoTitle} numberOfLines={1}>
                      {currentVideo.title}
                    </Text>
                    <Text style={styles.modernVideoCreator}>
                      {currentVideo.creator.username} • {formatViewCount(currentVideo.view_count)} views
                    </Text>
                  </View>

                  <View style={styles.topRightControls}>
                    <TouchableOpacity style={styles.modernControlButton} onPress={() => setShowAdvancedControls(true)}>
                      <Ionicons name="settings-outline" size={24} color="#FFFFFF" />
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.modernControlButton} onPress={handleShare}>
                      <Ionicons name="share-outline" size={24} color="#FFFFFF" />
                    </TouchableOpacity>
                  </View>
                </Animated.View>

                {/* Center Play Controls */}
                <Animated.View style={styles.centerControls} entering={FadeIn.delay(200)}>
                  <TouchableOpacity style={styles.modernRewindButton} onPress={handleRewind}>
                    <Ionicons name="play-back" size={32} color="#FFFFFF" />
                    <Text style={styles.skipText}>10s</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.modernPlayButton} onPress={handlePlayPause}>
                    <LinearGradient
                      colors={[GoGoColors.primary, GoGoColors.highlightGold]}
                      style={styles.playButtonGradient}
                    >
                      <Ionicons
                        name={isPlaying ? 'pause' : 'play'}
                        size={40}
                        color="#FFFFFF"
                        style={isPlaying ? {} : { marginLeft: 4 }}
                      />
                    </LinearGradient>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.modernForwardButton} onPress={handleFastForward}>
                    <Ionicons name="play-forward" size={32} color="#FFFFFF" />
                    <Text style={styles.skipText}>10s</Text>
                  </TouchableOpacity>
                </Animated.View>

                {/* Bottom Controls */}
                <Animated.View style={styles.bottomControls} entering={FadeIn.delay(300)}>
                  {/* Progress Bar */}
                  <View style={styles.modernProgressContainer}>
                    <Text style={styles.modernTimeText}>{formatTime(currentTime)}</Text>
                    <View style={styles.modernProgressBar}>
                      <Slider
                        style={styles.progressSlider}
                        minimumValue={0}
                        maximumValue={100}
                        value={getProgressPercentage()}
                        onValueChange={(value) => handleSeek(value)}
                        minimumTrackTintColor={GoGoColors.primary}
                        maximumTrackTintColor="rgba(255,255,255,0.3)"
                        thumbStyle={styles.progressThumbStyle}
                      />
                    </View>
                    <Text style={styles.modernTimeText}>{formatTime(duration)}</Text>
                  </View>

                  {/* Control Buttons Row */}
                  <View style={styles.modernControlsRow}>
                    <TouchableOpacity style={styles.modernControlButton} onPress={() => setShowSpeedSelector(true)}>
                      <Text style={styles.speedButtonText}>{playbackSpeed}x</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.modernControlButton} onPress={toggleSubtitles}>
                      <Ionicons
                        name="chatbox-outline"
                        size={20}
                        color={subtitles ? GoGoColors.primary : "#FFFFFF"}
                      />
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.modernControlButton} onPress={() => setShowQualitySelector(true)}>
                      <Text style={styles.qualityButtonText}>{quality.toUpperCase()}</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.modernControlButton} onPress={handleFullscreen}>
                      <Ionicons
                        name={isFullscreen ? "contract-outline" : "expand-outline"}
                        size={20}
                        color="#FFFFFF"
                      />
                    </TouchableOpacity>
                  </View>
                </Animated.View>
              </LinearGradient>
            </Animated.View>

            {/* Gesture Indicators */}
            <Animated.View style={[styles.volumeIndicator, { opacity: volumeIndicator }]}>
              <Ionicons name="volume-high" size={24} color="#FFFFFF" />
              <Text style={styles.indicatorText}>{Math.round(volume * 100)}%</Text>
            </Animated.View>

            <Animated.View style={[styles.seekIndicator, { opacity: seekIndicator }]}>
              <Ionicons name="play-forward" size={24} color="#FFFFFF" />
              <Text style={styles.indicatorText}>Seeking...</Text>
            </Animated.View>
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>

      {/* Enhanced Video Information Panel */}
      <Animated.View style={[styles.modernVideoInfo, videoInfoAnimatedStyle]} entering={SlideInDown.delay(400)}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Video Title and Creator */}
          <View style={styles.videoHeaderSection}>
            <Text style={styles.fullVideoTitle}>{currentVideo.title}</Text>
            <Text style={styles.videoStats}>
              {formatViewCount(currentVideo.view_count)} views • {new Date(currentVideo.created_at).toLocaleDateString()}
            </Text>
          </View>

          {/* Action Buttons Row */}
          <View style={styles.actionButtonsRow}>
            <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
              <LinearGradient
                colors={isLiked ? [GoGoColors.error, '#FF6B6B'] : ['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons
                  name={isLiked ? "heart" : "heart-outline"}
                  size={24}
                  color={isLiked ? "#FFFFFF" : GoGoColors.textPrimary}
                />
                <Text style={[styles.actionButtonText, { color: isLiked ? "#FFFFFF" : GoGoColors.textPrimary }]}>
                  {isLiked ? 'Liked' : 'Like'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleWatchLater}>
              <LinearGradient
                colors={isInWatchLater ? [GoGoColors.primary, GoGoColors.highlightGold] : ['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons
                  name={isInWatchLater ? "bookmark" : "bookmark-outline"}
                  size={24}
                  color={isInWatchLater ? "#FFFFFF" : GoGoColors.textPrimary}
                />
                <Text style={[styles.actionButtonText, { color: isInWatchLater ? "#FFFFFF" : GoGoColors.textPrimary }]}>
                  {isInWatchLater ? 'Saved' : 'Save'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
              <LinearGradient
                colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons name="share-outline" size={24} color={GoGoColors.textPrimary} />
                <Text style={styles.actionButtonText}>Share</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={() => Alert.alert('Download', 'Download feature coming soon!')}>
              <LinearGradient
                colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
                style={styles.actionButtonGradient}
              >
                <Ionicons name="download-outline" size={24} color={GoGoColors.textPrimary} />
                <Text style={styles.actionButtonText}>Download</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* Creator Information */}
          <View style={styles.creatorSection}>
            <View style={styles.creatorInfo}>
              <Image
                source={{ uri: currentVideo.creator.avatar_url || 'https://via.placeholder.com/50' }}
                style={styles.creatorAvatar}
              />
              <View style={styles.creatorDetails}>
                <Text style={styles.creatorName}>{currentVideo.creator.username}</Text>
                <Text style={styles.creatorSubscribers}>1.2M subscribers</Text>
              </View>
            </View>
            <TouchableOpacity style={styles.subscribeButton}>
              <LinearGradient
                colors={[GoGoColors.primary, GoGoColors.highlightGold]}
                style={styles.subscribeGradient}
              >
                <Text style={styles.subscribeText}>Subscribe</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>

          {/* Video Description */}
          <View style={styles.descriptionSection}>
            <Text style={styles.descriptionTitle}>Description</Text>
            <Text style={styles.descriptionText} numberOfLines={3}>
              {currentVideo.description || 'No description available for this video.'}
            </Text>
            <TouchableOpacity>
              <Text style={styles.showMoreText}>Show more</Text>
            </TouchableOpacity>
          </View>

          {/* Comments and Related Videos Buttons */}
          <View style={styles.bottomActionsRow}>
            <TouchableOpacity style={styles.bottomActionButton} onPress={toggleComments}>
              <Ionicons name="chatbubble-outline" size={20} color={GoGoColors.textPrimary} />
              <Text style={styles.bottomActionText}>Comments (24)</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.bottomActionButton} onPress={toggleRelatedVideos}>
              <Ionicons name="list-outline" size={20} color={GoGoColors.textPrimary} />
              <Text style={styles.bottomActionText}>Related Videos</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </Animated.View>

      {/* Comments Modal */}
      <Animated.View style={[styles.commentsModal, commentsAnimatedStyle]}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Comments</Text>
          <TouchableOpacity onPress={toggleComments}>
            <Ionicons name="close" size={24} color={GoGoColors.textPrimary} />
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.commentsContent}>
          {/* Mock comments - replace with real data */}
          {[1, 2, 3, 4, 5].map((item) => (
            <View key={item} style={styles.commentItem}>
              <Image
                source={{ uri: 'https://via.placeholder.com/40' }}
                style={styles.commentAvatar}
              />
              <View style={styles.commentContent}>
                <Text style={styles.commentAuthor}>User {item}</Text>
                <Text style={styles.commentText}>This is a sample comment for the video. Great content!</Text>
                <Text style={styles.commentTime}>2 hours ago</Text>
              </View>
            </View>
          ))}
        </ScrollView>
      </Animated.View>

      {/* Related Videos Modal */}
      <Animated.View style={[styles.relatedVideosModal, relatedVideosAnimatedStyle]}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Related Videos</Text>
          <TouchableOpacity onPress={toggleRelatedVideos}>
            <Ionicons name="close" size={24} color={GoGoColors.textPrimary} />
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.relatedContent}>
          {/* Mock related videos - replace with real data */}
          {[1, 2, 3, 4, 5].map((item) => (
            <TouchableOpacity key={item} style={styles.relatedVideoItem}>
              <Image
                source={{ uri: 'https://via.placeholder.com/120x80' }}
                style={styles.relatedVideoThumbnail}
              />
              <View style={styles.relatedVideoInfo}>
                <Text style={styles.relatedVideoTitle}>Related Video Title {item}</Text>
                <Text style={styles.relatedVideoCreator}>Creator Name</Text>
                <Text style={styles.relatedVideoStats}>1.2M views • 2 days ago</Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </Animated.View>

      {/* Speed Selector Modal */}
      {showSpeedSelector && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.speedModal} entering={SlideInDown}>
              <Text style={styles.modalTitle}>Playback Speed</Text>
              {[0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0].map((speed) => (
                <TouchableOpacity
                  key={speed}
                  style={[styles.speedOption, playbackSpeed === speed && styles.speedOptionActive]}
                  onPress={() => handlePlaybackSpeedChange(speed)}
                >
                  <Text style={[styles.speedOptionText, playbackSpeed === speed && styles.speedOptionTextActive]}>
                    {speed}x {speed === 1.0 ? '(Normal)' : ''}
                  </Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          </View>
        </Modal>
      )}

      {/* Quality Selector Modal */}
      {showQualitySelector && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.qualityModal} entering={SlideInDown}>
              <Text style={styles.modalTitle}>Video Quality</Text>
              {['auto', '1080p', '720p', '480p', '360p'].map((qual) => (
                <TouchableOpacity
                  key={qual}
                  style={[styles.qualityOption, quality === qual && styles.qualityOptionActive]}
                  onPress={() => handleQualityChange(qual)}
                >
                  <Text style={[styles.qualityOptionText, quality === qual && styles.qualityOptionTextActive]}>
                    {qual.toUpperCase()} {qual === 'auto' ? '(Recommended)' : ''}
                  </Text>
                </TouchableOpacity>
              ))}
            </Animated.View>
          </View>
        </Modal>
      )}

      {/* Advanced Controls Modal */}
      {showAdvancedControls && (
        <Modal transparent animationType="fade">
          <View style={styles.modalOverlay}>
            <Animated.View style={styles.advancedModal} entering={SlideInDown}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Advanced Controls</Text>
                <TouchableOpacity onPress={() => setShowAdvancedControls(false)}>
                  <Ionicons name="close" size={24} color={GoGoColors.textPrimary} />
                </TouchableOpacity>
              </View>

              <View style={styles.advancedControlsContent}>
                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Subtitles</Text>
                  <TouchableOpacity
                    style={[styles.toggleButton, subtitles && styles.toggleButtonActive]}
                    onPress={toggleSubtitles}
                  >
                    <Text style={[styles.toggleButtonText, subtitles && styles.toggleButtonTextActive]}>
                      {subtitles ? 'ON' : 'OFF'}
                    </Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Auto-play Next</Text>
                  <TouchableOpacity style={styles.toggleButton}>
                    <Text style={styles.toggleButtonText}>OFF</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.controlRow}>
                  <Text style={styles.controlLabel}>Loop Video</Text>
                  <TouchableOpacity style={styles.toggleButton}>
                    <Text style={styles.toggleButtonText}>OFF</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </View>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
  },
  loadingText: {
    color: GoGoColors.textPrimary,
    fontSize: 18,
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundDark,
    padding: 20,
  },
  accessDeniedTitle: {
    color: GoGoColors.textPrimary,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  accessDeniedText: {
    color: GoGoColors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 24,
  },
  upgradeButton: {
    backgroundColor: GoGoColors.primary,
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 15,
  },
  upgradeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  // Modern Video Player Styles
  videoContainer: {
    height: height * 0.35,
    backgroundColor: '#000000',
    position: 'relative',
  },
  videoTouchable: {
    width: '100%',
    height: '100%',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  brightnessOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000000',
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  controlsGradient: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  // Top Controls
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modernBackButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  topCenterInfo: {
    flex: 1,
    marginHorizontal: 16,
  },
  modernVideoTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modernVideoCreator: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
    marginTop: 2,
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  topRightControls: {
    flexDirection: 'row',
    gap: 8,
  },
  modernControlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0,0,0,0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Center Controls
  centerControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 40,
  },
  modernRewindButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  modernForwardButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  skipText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 10,
    marginTop: 4,
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modernPlayButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  playButtonGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // Bottom Controls
  bottomControls: {
    gap: 16,
  },
  modernProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  modernTimeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    minWidth: 40,
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  modernProgressBar: {
    flex: 1,
    height: 40,
  },
  progressSlider: {
    width: '100%',
    height: 40,
  },
  progressThumbStyle: {
    width: 16,
    height: 16,
    backgroundColor: GoGoColors.primary,
  },
  modernControlsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  speedButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  qualityButtonText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  // Gesture Indicators
  volumeIndicator: {
    position: 'absolute',
    top: '50%',
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    transform: [{ translateY: -25 }],
  },
  seekIndicator: {
    position: 'absolute',
    top: '50%',
    left: 20,
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    transform: [{ translateY: -25 }],
  },
  indicatorText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  // Modern Video Info Panel
  modernVideoInfo: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -24,
    paddingTop: 24,
  },
  videoHeaderSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  fullVideoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    lineHeight: 28,
    marginBottom: 8,
  },
  videoStats: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
  },
  // Action Buttons
  actionButtonsRow: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  actionButtonGradient: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: 'center',
    gap: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: GoGoColors.textPrimary,
  },
  // Creator Section
  creatorSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  creatorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  creatorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  creatorDetails: {
    flex: 1,
  },
  creatorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 2,
  },
  creatorSubscribers: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
  },
  subscribeButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  subscribeGradient: {
    paddingHorizontal: 24,
    paddingVertical: 10,
  },
  subscribeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  // Description Section
  descriptionSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  descriptionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  showMoreText: {
    fontSize: 14,
    color: GoGoColors.primary,
    fontWeight: '600',
  },
  // Bottom Actions
  bottomActionsRow: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  bottomActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  bottomActionText: {
    fontSize: 14,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  // Modals
  commentsModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: height * 0.7,
    backgroundColor: GoGoColors.backgroundDark,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  relatedVideosModal: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: height * 0.7,
    backgroundColor: GoGoColors.backgroundDark,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  // Comments
  commentsContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  commentItem: {
    flexDirection: 'row',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  commentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  commentContent: {
    flex: 1,
  },
  commentAuthor: {
    fontSize: 14,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  commentText: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    lineHeight: 20,
    marginBottom: 4,
  },
  commentTime: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  // Related Videos
  relatedContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  relatedVideoItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  relatedVideoThumbnail: {
    width: 120,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  relatedVideoInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  relatedVideoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    lineHeight: 18,
    marginBottom: 4,
  },
  relatedVideoCreator: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    marginBottom: 2,
  },
  relatedVideoStats: {
    fontSize: 11,
    color: GoGoColors.textMuted,
  },
  // Modal Overlays
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  speedModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 300,
  },
  speedOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: 'transparent',
  },
  speedOptionActive: {
    backgroundColor: GoGoColors.primary,
  },
  speedOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    fontWeight: '500',
  },
  speedOptionTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  qualityModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 300,
  },
  qualityOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginVertical: 4,
    backgroundColor: 'transparent',
  },
  qualityOptionActive: {
    backgroundColor: GoGoColors.primary,
  },
  qualityOptionText: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    textAlign: 'center',
    fontWeight: '500',
  },
  qualityOptionTextActive: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  // Advanced Controls Modal
  advancedModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 350,
  },
  advancedControlsContent: {
    paddingTop: 16,
  },
  controlRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  controlLabel: {
    fontSize: 16,
    color: GoGoColors.textPrimary,
    fontWeight: '500',
  },
  toggleButton: {
    backgroundColor: GoGoColors.backgroundLight,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    minWidth: 60,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: GoGoColors.primary,
  },
  toggleButtonText: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    fontWeight: 'bold',
  },
  toggleButtonTextActive: {
    color: '#FFFFFF',
  },
  },
  closeButtonText: {
    color: GoGoColors.textMuted,
    fontSize: 16,
  },
  videoContainer: {
    height: width * 0.56, // 16:9 aspect ratio
    position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  controlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  videoTitleContainer: {
    flex: 1,
    marginLeft: 15,
  },
  videoTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  videoCreator: {
    color: '#ccc',
    fontSize: 14,
  },
  centerControls: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  playPauseButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  playPauseText: {
    color: '#fff',
    fontSize: 32,
  },
  bottomControls: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  progressContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    color: '#fff',
    fontSize: 12,
    minWidth: 40,
    textAlign: 'center',
  },
  progressBar: {
    flex: 1,
    height: 20,
    marginHorizontal: 10,
    position: 'relative',
    justifyContent: 'center',
  },
  progressTrack: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
  },
  progressFill: {
    position: 'absolute',
    height: 4,
    backgroundColor: GoGoColors.primary,
    borderRadius: 2,
  },
  progressThumb: {
    position: 'absolute',
    width: 12,
    height: 12,
    backgroundColor: GoGoColors.primary,
    borderRadius: 6,
    marginTop: -4,
    marginLeft: -6,
  },
  rightControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fullscreenButton: {
    padding: 10,
  },
  fullscreenText: {
    color: '#fff',
    fontSize: 20,
  },
  videoInfo: {
    padding: 20,
  },
  infoTitle: {
    color: GoGoColors.textPrimary,
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  infoDescription: {
    color: GoGoColors.textSecondary,
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 15,
  },
  videoStats: {
    flexDirection: 'row',
    gap: 20,
  },
  statText: {
    color: GoGoColors.textMuted,
    fontSize: 12,
  },
  // Enhanced control styles
  speedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  controlButton: {
    padding: 8,
    marginHorizontal: 4,
    borderRadius: 4,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  speedSelectorModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    padding: 20,
    minWidth: 250,
  },
  modalTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  speedButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 10,
    marginBottom: 20,
  },
  speedButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  activeSpeedButton: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  speedButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  activeSpeedButtonText: {
    fontWeight: 'bold',
  },
  modalCloseButton: {
    backgroundColor: GoGoColors.glassBg,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: GoGoColors.glassBorder,
  },
  modalCloseButtonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  settingsModal: {
    backgroundColor: GoGoColors.backgroundCard,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    width: '100%',
    position: 'absolute',
    bottom: 0,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  modalHeaderButton: {
    padding: 4,
  },
  settingsContent: {
    padding: 20,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: GoGoColors.border,
  },
  settingLabel: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  settingValueText: {
    color: GoGoColors.textSecondary,
    fontSize: 14,
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
    maxWidth: 150,
  },
  settingSlider: {
    flex: 1,
    height: 20,
  },
  sliderValue: {
    color: 'white',
    fontSize: 12,
    minWidth: 35,
    textAlign: 'right',
  },
  settingToggle: {
    padding: 4,
  },
  toggleSwitch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: GoGoColors.glassBg,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleSwitchActive: {
    backgroundColor: GoGoColors.primary,
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: 'white',
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
});

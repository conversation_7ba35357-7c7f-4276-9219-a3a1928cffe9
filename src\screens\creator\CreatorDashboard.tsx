import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  Dimensions,
  StatusBar,
  RefreshControl,
  FlatList,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  FadeIn,
  SlideInDown,
  SlideInRight,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { GoGoColors } from '../../../constants/Colors';
import { hapticFeedback } from '../../utils/animations';
import { formatViewCount, formatDuration } from '../../utils/formatters';

const { width, height } = Dimensions.get('window');

// Stats Card Component
interface StatsCardProps {
  icon: string;
  value: string;
  label: string;
  color: string;
  index: number;
  trend?: string;
}

function StatsCard({ icon, value, label, color, index, trend }: StatsCardProps) {
  return (
    <Animated.View style={styles.statsCard} entering={FadeIn.delay(index * 150)}>
      <LinearGradient
        colors={[color + '15', color + '05']}
        style={styles.statsGradient}
      >
        <View style={[styles.statsIcon, { backgroundColor: color + '20' }]}>
          <Ionicons name={icon as any} size={24} color={color} />
        </View>
        <Text style={styles.statsValue}>{value}</Text>
        <Text style={styles.statsLabel}>{label}</Text>
        {trend && (
          <View style={styles.trendContainer}>
            <Ionicons 
              name={trend.startsWith('+') ? 'trending-up' : 'trending-down'} 
              size={12} 
              color={trend.startsWith('+') ? GoGoColors.success : GoGoColors.error} 
            />
            <Text style={[
              styles.trendText, 
              { color: trend.startsWith('+') ? GoGoColors.success : GoGoColors.error }
            ]}>
              {trend}
            </Text>
          </View>
        )}
      </LinearGradient>
    </Animated.View>
  );
}

// Quick Action Card Component
interface QuickActionProps {
  icon: string;
  title: string;
  subtitle: string;
  color: string;
  onPress: () => void;
  index: number;
}

function QuickActionCard({ icon, title, subtitle, color, onPress, index }: QuickActionProps) {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View 
      style={[styles.quickActionCard, animatedStyle]} 
      entering={SlideInRight.delay(index * 100)}
    >
      <TouchableOpacity
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
        style={styles.quickActionTouchable}
      >
        <LinearGradient
          colors={[color, color + 'DD']}
          style={styles.quickActionGradient}
        >
          <View style={styles.quickActionIcon}>
            <Ionicons name={icon as any} size={28} color="#FFFFFF" />
          </View>
          <View style={styles.quickActionText}>
            <Text style={styles.quickActionTitle}>{title}</Text>
            <Text style={styles.quickActionSubtitle}>{subtitle}</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color="#FFFFFF" />
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
}

export default function CreatorDashboard() {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { videos } = useAppSelector((state) => state.video);
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');

  // Mock creator stats - in real app, fetch from API
  const creatorStats = {
    totalViews: '2.4M',
    totalVideos: '127',
    subscribers: '45.2K',
    revenue: '$3,247',
    avgWatchTime: '4:32',
    engagement: '8.7%',
  };

  // Mock recent videos - filter user's videos
  const myVideos = videos.filter(video => video.creator.id === user?.id).slice(0, 5);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => setRefreshing(false), 2000);
  };

  const handleUploadVideo = () => {
    hapticFeedback.light();
    Alert.alert('Upload Video', 'Video upload feature coming soon!');
  };

  const handleAnalytics = () => {
    hapticFeedback.light();
    Alert.alert('Analytics', 'Detailed analytics coming soon!');
  };

  const handleManageVideos = () => {
    hapticFeedback.light();
    Alert.alert('Manage Videos', 'Video management coming soon!');
  };

  const handleEarnings = () => {
    hapticFeedback.light();
    Alert.alert('Earnings', 'Earnings dashboard coming soon!');
  };

  const handleLiveStream = () => {
    hapticFeedback.light();
    Alert.alert('Live Stream', 'Live streaming feature coming soon!');
  };

  const handleSettings = () => {
    hapticFeedback.light();
    Alert.alert('Creator Settings', 'Creator settings coming soon!');
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={GoGoColors.backgroundDark} />
      
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[GoGoColors.primary]}
            tintColor={GoGoColors.primary}
          />
        }
      >
        {/* Header */}
        <Animated.View style={styles.header} entering={FadeIn}>
          <LinearGradient
            colors={GoGoColors.backgroundGradient}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <View style={styles.headerTop}>
                <View>
                  <Text style={styles.welcomeText}>Creator Dashboard</Text>
                  <Text style={styles.headerTitle}>Welcome back, {user?.full_name?.split(' ')[0]}! 🎬</Text>
                </View>
                <TouchableOpacity style={styles.settingsButton} onPress={handleSettings}>
                  <Ionicons name="settings-outline" size={24} color={GoGoColors.textPrimary} />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Period Selector */}
        <Animated.View style={styles.periodSelector} entering={SlideInDown.delay(200)}>
          <Text style={styles.sectionTitle}>Analytics Period</Text>
          <View style={styles.periodButtons}>
            {(['week', 'month', 'year'] as const).map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.periodButton,
                  selectedPeriod === period && styles.periodButtonActive
                ]}
                onPress={() => {
                  hapticFeedback.light();
                  setSelectedPeriod(period);
                }}
              >
                <Text style={[
                  styles.periodButtonText,
                  selectedPeriod === period && styles.periodButtonTextActive
                ]}>
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Stats Grid */}
        <Animated.View style={styles.statsSection} entering={SlideInDown.delay(400)}>
          <Text style={styles.sectionTitle}>Your Performance</Text>
          <View style={styles.statsGrid}>
            <StatsCard
              icon="eye"
              value={creatorStats.totalViews}
              label="Total Views"
              color={GoGoColors.primary}
              index={0}
              trend="+12.5%"
            />
            <StatsCard
              icon="videocam"
              value={creatorStats.totalVideos}
              label="Videos"
              color={GoGoColors.highlightGold}
              index={1}
              trend="+3"
            />
            <StatsCard
              icon="people"
              value={creatorStats.subscribers}
              label="Subscribers"
              color={GoGoColors.success}
              index={2}
              trend="+8.2%"
            />
            <StatsCard
              icon="cash"
              value={creatorStats.revenue}
              label="Revenue"
              color={GoGoColors.error}
              index={3}
              trend="+15.3%"
            />
          </View>
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View style={styles.quickActionsSection} entering={SlideInDown.delay(600)}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <QuickActionCard
              icon="add-circle"
              title="Upload Video"
              subtitle="Share new content"
              color={GoGoColors.primary}
              onPress={handleUploadVideo}
              index={0}
            />
            <QuickActionCard
              icon="bar-chart"
              title="Analytics"
              subtitle="View detailed stats"
              color={GoGoColors.highlightGold}
              onPress={handleAnalytics}
              index={1}
            />
            <QuickActionCard
              icon="library"
              title="Manage Videos"
              subtitle="Edit your content"
              color={GoGoColors.success}
              onPress={handleManageVideos}
              index={2}
            />
            <QuickActionCard
              icon="wallet"
              title="Earnings"
              subtitle="Track your income"
              color={GoGoColors.error}
              onPress={handleEarnings}
              index={3}
            />
          </View>
        </Animated.View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  scrollContainer: {
    flex: 1,
  },
  // Header Styles
  header: {
    marginBottom: 24,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 32,
    paddingHorizontal: 20,
  },
  headerContent: {
    flex: 1,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  welcomeText: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    marginBottom: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  settingsButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  // Section Styles
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  // Period Selector
  periodSelector: {
    marginBottom: 24,
  },
  periodButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundLight,
    borderWidth: 1,
    borderColor: GoGoColors.border,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.textSecondary,
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  // Stats Styles
  statsSection: {
    marginBottom: 32,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 16,
  },
  statsCard: {
    width: (width - 56) / 2,
    borderRadius: 16,
    overflow: 'hidden',
  },
  statsGradient: {
    padding: 20,
    alignItems: 'center',
  },
  statsIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 4,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  // Quick Actions
  quickActionsSection: {
    marginBottom: 32,
  },
  quickActionsGrid: {
    paddingHorizontal: 20,
    gap: 16,
  },
  quickActionCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  quickActionTouchable: {
    flex: 1,
  },
  quickActionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  quickActionText: {
    flex: 1,
  },
  quickActionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  quickActionSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  bottomSpacing: {
    height: 32,
  },
});

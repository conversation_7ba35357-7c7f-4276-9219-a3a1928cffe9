import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  withSpring,
  useSharedValue,
  withTiming,
  FadeIn,
  SlideInDown,
} from 'react-native-reanimated';
import { useAppDispatch, useAppSelector } from '../../store';
import { searchVideos, clearSearchResults, fetchCategories } from '../../store/slices/videoSlice';
import { Video, Category } from '../../types';
import { GoGoColors } from '../../../constants/Colors';
import { useResponsiveLayout } from '../../utils/responsive';
import { hapticFeedback } from '../../utils/animations';
import { formatViewCount, formatDuration } from '../../utils/formatters';

const { width, height } = Dimensions.get('window');

// Modern Search Result Card Component
interface ModernSearchResultProps {
  video: Video;
  onPress: (video: Video) => void;
  index: number;
  viewMode: 'list' | 'grid';
}

function ModernSearchResult({ video, onPress, index, viewMode }: ModernSearchResultProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(0);

  useEffect(() => {
    opacity.value = withTiming(1, { duration: 300 + index * 50 });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  if (viewMode === 'grid') {
    return (
      <Animated.View style={[styles.gridResultCard, animatedStyle]} entering={FadeIn.delay(index * 100)}>
        <TouchableOpacity
          onPress={() => onPress(video)}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
          style={styles.gridCardTouchable}
        >
          <View style={styles.gridImageContainer}>
            <Image source={{ uri: video.thumbnail_url }} style={styles.gridImage} />
            {video.is_premium && (
              <View style={styles.gridPremiumBadge}>
                <Ionicons name="diamond" size={10} color="#FFFFFF" />
              </View>
            )}
            <View style={styles.gridPlayOverlay}>
              <Ionicons name="play" size={16} color="#FFFFFF" />
            </View>
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.6)']}
              style={styles.gridGradient}
            >
              <Text style={styles.gridDuration}>{formatDuration(video.duration)}</Text>
            </LinearGradient>
          </View>
          <View style={styles.gridContent}>
            <Text style={styles.gridTitle} numberOfLines={2}>{video.title}</Text>
            <Text style={styles.gridCreator} numberOfLines={1}>{video.creator.username}</Text>
            <Text style={styles.gridViews}>{formatViewCount(video.view_count)} views</Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[styles.listResultCard, animatedStyle]} entering={SlideInDown.delay(index * 50)}>
      <TouchableOpacity
        onPress={() => onPress(video)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
        style={styles.listCardTouchable}
      >
        <View style={styles.listImageContainer}>
          <Image source={{ uri: video.thumbnail_url }} style={styles.listImage} />
          {video.is_premium && (
            <View style={styles.listPremiumBadge}>
              <Ionicons name="diamond" size={8} color="#FFFFFF" />
            </View>
          )}
          <View style={styles.listPlayOverlay}>
            <Ionicons name="play" size={14} color="#FFFFFF" />
          </View>
          <View style={styles.listDurationBadge}>
            <Text style={styles.listDurationText}>{formatDuration(video.duration)}</Text>
          </View>
        </View>
        <View style={styles.listContent}>
          <Text style={styles.listTitle} numberOfLines={2}>{video.title}</Text>
          <Text style={styles.listCreator}>{video.creator.username}</Text>
          <View style={styles.listStats}>
            <Text style={styles.listStatText}>{formatViewCount(video.view_count)} views</Text>
            <Text style={styles.listStatDot}>•</Text>
            <Text style={styles.listStatText}>{video.category?.name || 'General'}</Text>
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

// Modern Category Chip Component
interface ModernCategoryChipProps {
  category: Category;
  isSelected: boolean;
  onPress: (category: Category) => void;
  index: number;
}

function ModernCategoryChip({ category, isSelected, onPress, index }: ModernCategoryChipProps) {
  const scale = useSharedValue(1);

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
    hapticFeedback.light();
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Animated.View style={animatedStyle} entering={FadeIn.delay(index * 50)}>
      <TouchableOpacity
        style={[styles.modernCategoryChip, isSelected && styles.modernCategoryChipSelected]}
        onPress={() => onPress(category)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <LinearGradient
          colors={isSelected ? [GoGoColors.primary, GoGoColors.primaryDark] : ['transparent', 'transparent']}
          style={styles.categoryChipGradient}
        >
          <Text style={[styles.modernCategoryChipText, isSelected && styles.modernCategoryChipTextSelected]}>
            {category.name}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
}

// Trending Search Item Component
interface TrendingSearchProps {
  search: string;
  onPress: (search: string) => void;
  index: number;
}

function TrendingSearchItem({ search, onPress, index }: TrendingSearchProps) {
  return (
    <Animated.View entering={SlideInDown.delay(index * 100)}>
      <TouchableOpacity
        style={styles.trendingSearchItem}
        onPress={() => onPress(search)}
      >
        <View style={styles.trendingSearchIcon}>
          <Ionicons name="trending-up" size={16} color={GoGoColors.primary} />
        </View>
        <Text style={styles.trendingSearchText}>{search}</Text>
        <Ionicons name="arrow-forward" size={16} color={GoGoColors.textMuted} />
      </TouchableOpacity>
    </Animated.View>
  );
}

interface Props {
  onVideoPress: (video: Video) => void;
}

export default function SearchScreen({ onVideoPress }: Props) {
  const dispatch = useAppDispatch();
  const { searchResults, categories, isLoading } = useAppSelector((state) => state.video);
  const layout = useResponsiveLayout();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [showFilters, setShowFilters] = useState(false);
  const [isVoiceSearchActive, setIsVoiceSearchActive] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);

  const [trendingSearches] = useState<string[]>([
    'Action & Adventure',
    'Comedy Specials',
    'Sci-Fi Movies',
    'Documentary Series',
    'Cooking Shows',
    'Travel Vlogs',
  ]);

  const [filters, setFilters] = useState({
    duration: 'any',
    quality: 'any',
    price: 'any',
    sortBy: 'relevance',
  });

  const searchInputRef = useRef<TextInput>(null);
  const searchBarScale = useSharedValue(1);
  const filterOpacity = useSharedValue(0);

  useEffect(() => {
    dispatch(fetchCategories());
  }, [dispatch]);

  useEffect(() => {
    if (searchQuery.trim().length > 2) {
      setIsSearching(true);
      const timeoutId = setTimeout(() => {
        dispatch(searchVideos(searchQuery.trim()));
        setIsSearching(false);
      }, 300);
      return () => clearTimeout(timeoutId);
    } else {
      dispatch(clearSearchResults());
      setIsSearching(false);
    }
  }, [searchQuery, dispatch]);

  useEffect(() => {
    searchBarScale.value = withSpring(searchFocused ? 1.02 : 1);
  }, [searchFocused]);

  const handleCategoryPress = (category: Category) => {
    hapticFeedback.light();
    if (selectedCategory?.id === category.id) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(category);
    }
  };

  const clearSearch = () => {
    hapticFeedback.light();
    setSearchQuery('');
    setSelectedCategory(null);
    dispatch(clearSearchResults());
    setSearchFocused(false);
    searchInputRef.current?.blur();
  };

  const handleTrendingSearchPress = (search: string) => {
    hapticFeedback.light();
    setSearchQuery(search);
    searchInputRef.current?.focus();
  };

  const handleVoiceSearch = () => {
    hapticFeedback.medium();
    setIsVoiceSearchActive(!isVoiceSearchActive);
    // Voice search implementation would go here
  };

  const toggleFilters = () => {
    hapticFeedback.light();
    setShowFilters(!showFilters);
    filterOpacity.value = withTiming(showFilters ? 0 : 1, { duration: 300 });
  };

  const toggleViewMode = () => {
    hapticFeedback.light();
    setViewMode(prev => prev === 'list' ? 'grid' : 'list');
  };

  const searchBarAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: searchBarScale.value }],
  }));

  const filterAnimatedStyle = useAnimatedStyle(() => ({
    opacity: filterOpacity.value,
    height: filterOpacity.value * 60,
  }));

  const renderSearchResult = ({ item, index }: { item: Video; index: number }) => (
    <ModernSearchResult
      video={item}
      onPress={onVideoPress}
      index={index}
      viewMode={viewMode}
    />
  );

  const renderCategory = ({ item, index }: { item: Category; index: number }) => (
    <ModernCategoryChip
      category={item}
      isSelected={selectedCategory?.id === item.id}
      onPress={handleCategoryPress}
      index={index}
    />
  );

  const renderTrendingSearch = ({ item, index }: { item: string; index: number }) => (
    <TrendingSearchItem
      search={item}
      onPress={handleTrendingSearchPress}
      index={index}
    />
  );

  return (
    <View style={styles.container}>
      {/* Modern Header with Gradient */}
      <LinearGradient
        colors={GoGoColors.backgroundGradient}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Search & Discover</Text>
          <Text style={styles.headerSubtitle}>Find your next favorite content</Text>
        </View>
      </LinearGradient>

      {/* Enhanced Search Bar */}
      <Animated.View style={[styles.searchContainer, searchBarAnimatedStyle]}>
        <View style={styles.modernSearchBar}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={GoGoColors.textMuted} style={styles.searchIcon} />
            <TextInput
              ref={searchInputRef}
              style={styles.searchInput}
              placeholder="Search videos, creators, topics..."
              placeholderTextColor={GoGoColors.textMuted}
              value={searchQuery}
              onChangeText={setSearchQuery}
              onFocus={() => setSearchFocused(true)}
              onBlur={() => setSearchFocused(false)}
              autoCorrect={false}
              returnKeyType="search"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                <Ionicons name="close-circle" size={20} color={GoGoColors.textMuted} />
              </TouchableOpacity>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.searchActions}>
            <TouchableOpacity
              style={[styles.actionButton, isVoiceSearchActive && styles.actionButtonActive]}
              onPress={handleVoiceSearch}
            >
              <Ionicons
                name={isVoiceSearchActive ? "mic" : "mic-outline"}
                size={18}
                color={isVoiceSearchActive ? "#FFFFFF" : GoGoColors.textSecondary}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, showFilters && styles.actionButtonActive]}
              onPress={toggleFilters}
            >
              <Ionicons
                name="options-outline"
                size={18}
                color={showFilters ? "#FFFFFF" : GoGoColors.textSecondary}
              />
            </TouchableOpacity>

            {searchResults.length > 0 && (
              <TouchableOpacity style={styles.actionButton} onPress={toggleViewMode}>
                <Ionicons
                  name={viewMode === 'list' ? "grid-outline" : "list-outline"}
                  size={18}
                  color={GoGoColors.textSecondary}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Animated.View>

      {/* Advanced Filters */}
      {showFilters && (
        <Animated.View style={[styles.filtersContainer, filterAnimatedStyle]}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filtersScroll}>
            <TouchableOpacity
              style={[styles.modernFilterChip, filters.price === 'free' && styles.modernFilterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, price: prev.price === 'free' ? 'any' : 'free' }))}
            >
              <Ionicons name="gift-outline" size={14} color={filters.price === 'free' ? "#FFFFFF" : GoGoColors.textSecondary} />
              <Text style={[styles.modernFilterChipText, filters.price === 'free' && styles.modernFilterChipTextActive]}>
                Free
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modernFilterChip, filters.price === 'premium' && styles.modernFilterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, price: prev.price === 'premium' ? 'any' : 'premium' }))}
            >
              <Ionicons name="diamond-outline" size={14} color={filters.price === 'premium' ? "#FFFFFF" : GoGoColors.textSecondary} />
              <Text style={[styles.modernFilterChipText, filters.price === 'premium' && styles.modernFilterChipTextActive]}>
                Premium
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modernFilterChip, filters.duration === 'short' && styles.modernFilterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, duration: prev.duration === 'short' ? 'any' : 'short' }))}
            >
              <Ionicons name="time-outline" size={14} color={filters.duration === 'short' ? "#FFFFFF" : GoGoColors.textSecondary} />
              <Text style={[styles.modernFilterChipText, filters.duration === 'short' && styles.modernFilterChipTextActive]}>
                Short
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modernFilterChip, filters.sortBy === 'recent' && styles.modernFilterChipActive]}
              onPress={() => setFilters(prev => ({ ...prev, sortBy: prev.sortBy === 'recent' ? 'relevance' : 'recent' }))}
            >
              <Ionicons name="calendar-outline" size={14} color={filters.sortBy === 'recent' ? "#FFFFFF" : GoGoColors.textSecondary} />
              <Text style={[styles.modernFilterChipText, filters.sortBy === 'recent' && styles.modernFilterChipTextActive]}>
                Recent
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </Animated.View>
      )}

      <ScrollView style={styles.contentContainer} showsVerticalScrollIndicator={false}>
        {/* Trending Searches */}
        {searchQuery.length === 0 && (
          <View style={styles.trendingSection}>
            <Text style={styles.sectionTitle}>Trending Searches</Text>
            <FlatList
              data={trendingSearches}
              keyExtractor={(item, index) => `trending-${index}`}
              renderItem={renderTrendingSearch}
              scrollEnabled={false}
              contentContainerStyle={styles.trendingList}
            />
          </View>
        )}

        {/* Categories */}
        {searchQuery.length === 0 && (
          <View style={styles.categoriesSection}>
            <Text style={styles.sectionTitle}>Browse Categories</Text>
            <FlatList
              data={categories}
              horizontal
              showsHorizontalScrollIndicator={false}
              keyExtractor={(item) => item.id}
              renderItem={renderCategory}
              contentContainerStyle={styles.categoriesList}
            />
          </View>
        )}

        {/* Search Results */}
        {searchQuery.length > 0 && (
          <View style={styles.resultsSection}>
            {isSearching ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={GoGoColors.primary} />
                <Text style={styles.loadingText}>Searching for "{searchQuery}"...</Text>
              </View>
            ) : searchResults.length > 0 ? (
              <>
                <View style={styles.resultsHeader}>
                  <Text style={styles.resultsTitle}>
                    {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                  </Text>
                  <Text style={styles.resultsSubtitle}>for "{searchQuery}"</Text>
                </View>
                <FlatList
                  data={searchResults}
                  keyExtractor={(item) => item.id}
                  renderItem={renderSearchResult}
                  numColumns={viewMode === 'grid' ? (layout.isTablet ? 3 : 2) : 1}
                  key={viewMode} // Force re-render when view mode changes
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.resultsList}
                  scrollEnabled={false}
                />
              </>
            ) : (
              <View style={styles.noResultsContainer}>
                <Ionicons name="search-outline" size={64} color={GoGoColors.textMuted} />
                <Text style={styles.noResultsTitle}>No results found</Text>
                <Text style={styles.noResultsText}>
                  Try adjusting your search terms or browse our categories
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Empty State */}
        {searchQuery.length === 0 && (
          <View style={styles.emptyStateContainer}>
            <LinearGradient
              colors={[GoGoColors.primary + '20', GoGoColors.primary + '05']}
              style={styles.emptyStateGradient}
            >
              <Ionicons name="search" size={80} color={GoGoColors.primary} />
              <Text style={styles.emptyStateTitle}>Discover Amazing Content</Text>
              <Text style={styles.emptyStateText}>
                Search for videos, creators, or browse by category to find your next favorite content.
              </Text>
            </LinearGradient>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GoGoColors.backgroundDark,
  },
  // Header Styles
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  headerContent: {
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
  },
  // Search Bar Styles
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  modernSearchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 16,
    paddingHorizontal: 16,
    height: 52,
    borderWidth: 1,
    borderColor: GoGoColors.border,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    color: GoGoColors.textPrimary,
    fontSize: 16,
    fontWeight: '500',
  },
  clearButton: {
    padding: 4,
  },
  searchActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 12,
    backgroundColor: GoGoColors.backgroundCard,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  actionButtonActive: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  // Filter Styles
  filtersContainer: {
    paddingHorizontal: 20,
    overflow: 'hidden',
  },
  filtersScroll: {
    gap: 12,
    paddingVertical: 8,
  },
  modernFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: GoGoColors.border,
    gap: 6,
  },
  modernFilterChipActive: {
    backgroundColor: GoGoColors.primary,
    borderColor: GoGoColors.primary,
  },
  modernFilterChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.textSecondary,
  },
  modernFilterChipTextActive: {
    color: '#FFFFFF',
  },
  // Content Styles
  contentContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  // Trending Searches
  trendingSection: {
    marginBottom: 32,
  },
  trendingList: {
    paddingHorizontal: 20,
    gap: 8,
  },
  trendingSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GoGoColors.backgroundCard,
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  trendingSearchIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: GoGoColors.backgroundLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  trendingSearchText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: GoGoColors.textPrimary,
  },
  // Categories
  categoriesSection: {
    marginBottom: 32,
  },
  categoriesList: {
    paddingHorizontal: 20,
    gap: 12,
  },
  modernCategoryChip: {
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: GoGoColors.border,
  },
  modernCategoryChipSelected: {
    borderColor: GoGoColors.primary,
  },
  categoryChipGradient: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  modernCategoryChipText: {
    fontSize: 14,
    fontWeight: '600',
    color: GoGoColors.textSecondary,
    textAlign: 'center',
  },
  modernCategoryChipTextSelected: {
    color: '#FFFFFF',
  },
  // Search Results
  resultsSection: {
    flex: 1,
    paddingBottom: 20,
  },
  resultsHeader: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
  },
  resultsSubtitle: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginTop: 4,
  },
  resultsList: {
    paddingHorizontal: 20,
    gap: 16,
  },
  // Grid Result Card
  gridResultCard: {
    flex: 1,
    marginHorizontal: 4,
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: GoGoColors.border,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  gridCardTouchable: {
    flex: 1,
  },
  gridImageContainer: {
    position: 'relative',
    aspectRatio: 16 / 9,
  },
  gridImage: {
    width: '100%',
    height: '100%',
  },
  gridPremiumBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: GoGoColors.premium,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridPlayOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -16 }, { translateY: -16 }],
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '30%',
    justifyContent: 'flex-end',
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  gridDuration: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    alignSelf: 'flex-end',
  },
  gridContent: {
    padding: 12,
  },
  gridTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginBottom: 4,
    lineHeight: 18,
  },
  gridCreator: {
    fontSize: 12,
    color: GoGoColors.textSecondary,
    marginBottom: 4,
  },
  gridViews: {
    fontSize: 11,
    color: GoGoColors.textMuted,
  },
  // List Result Card
  listResultCard: {
    backgroundColor: GoGoColors.backgroundCard,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: GoGoColors.border,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  listCardTouchable: {
    flexDirection: 'row',
    padding: 12,
  },
  listImageContainer: {
    position: 'relative',
    width: 120,
    height: 68,
    borderRadius: 8,
    overflow: 'hidden',
  },
  listImage: {
    width: '100%',
    height: '100%',
  },
  listPremiumBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: GoGoColors.premium,
    width: 16,
    height: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listPlayOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -14 }, { translateY: -14 }],
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  listDurationBadge: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  listDurationText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  listContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'space-between',
  },
  listTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    lineHeight: 20,
    marginBottom: 4,
  },
  listCreator: {
    fontSize: 14,
    color: GoGoColors.textSecondary,
    marginBottom: 8,
  },
  listStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listStatText: {
    fontSize: 12,
    color: GoGoColors.textMuted,
  },
  listStatDot: {
    fontSize: 12,
    color: GoGoColors.textMuted,
    marginHorizontal: 6,
  },
  // Loading and Empty States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    marginTop: 16,
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  noResultsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noResultsText: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  emptyStateContainer: {
    flex: 1,
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyStateGradient: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GoGoColors.textPrimary,
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: GoGoColors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
